#!/usr/bin/.env python3
# -*- coding: utf-8 -*-
"""
TPDOG版块资金流监控程序
专门获取TPDOG的概念、行业资金流数据

功能：
1. 收盘时获取版块列表（如果不存在）
2. 交易时间获取版块资金流数据
3. 获取前10版块的个股资金流
4. 获取版块实时行情数据
5. 定时保存数据到本地

开放时间：交易日09:30~11:30/13:00~15:00
"""

import requests
import pandas as pd
import schedule
import time as time_module
from datetime import datetime, time
from dotenv import load_dotenv
import os
import logging
import json
import threading
import warnings

# 忽略pandas的FutureWarning
warnings.filterwarnings("ignore", category=FutureWarning, module="pandas")

# 加载环境变量
load_dotenv()

# 导入资金断层检测功能
try:
    from dynamic_gap_detector import (
        analyze_funding_gap_v7,
        analyze_stock_flow_gap,
        format_amount,
        convert_to_float,
        MIN_SECTORS_FOR_ANALYSIS,
        StockFlowIgnitionDetector
    )
    GAP_DETECTOR_AVAILABLE = True
    print("✅ 资金断层检测模块加载成功")
except ImportError as e:
    GAP_DETECTOR_AVAILABLE = False
    print(f"⚠️ 资金断层检测模块加载失败: {e}")
    print("   断层检测功能将不可用")

# 配置日志
def setup_logging():
    """设置日志配置"""
    today_str = datetime.now().strftime('%Y%m%d')
    log_dir = os.path.join('data', today_str)
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'tpdog_monitor.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

# 全局配置
TPDOG_TOKEN = os.getenv('TPDOG_TOKEN')
if not TPDOG_TOKEN:
    print("⚠️ 警告：未在.env文件中配置TPDOG_TOKEN，程序无法运行")
    print("   请在.env文件中设置: TPDOG_TOKEN=your_token")
    exit(1)

# 交易时间配置
TRADING_START_1 = time(9, 30)
TRADING_END_1 = time(11, 30)
TRADING_START_2 = time(13, 0)
TRADING_END_2 = time(15, 0)

def is_trading_time():
    """检查是否为交易时间"""
    now = datetime.now().time()
    return (TRADING_START_1 <= now <= TRADING_END_1) or (TRADING_START_2 <= now <= TRADING_END_2)

def get_date_folder():
    """获取今日数据文件夹路径"""
    today_str = datetime.now().strftime('%Y%m%d')
    date_folder = os.path.join('data', today_str)
    os.makedirs(date_folder, exist_ok=True)
    return date_folder

def get_timestamp():
    """获取时间戳字符串"""
    return datetime.now().strftime('%Y%m%d_%H%M%S')

def format_amount_local(amount):
    """格式化金额显示（本地版本，避免与导入的format_amount冲突）"""
    if not isinstance(amount, (int, float)):
        return str(amount)
    if abs(amount) >= 1_0000_0000:
        return f"{amount / 1_0000_0000:.2f}亿"
    elif abs(amount) >= 1_0000:
        return f"{amount / 1_0000:.2f}万"
    else:
        return f"{amount:.2f}"

def analyze_sector_gap(sector_df, sector_type):
    """
    分析板块资金断层

    Args:
        sector_df: 板块资金流数据DataFrame
        sector_type: 板块类型 ('概念' 或 '行业')

    Returns:
        str: 断层分析报告
    """
    if not GAP_DETECTOR_AVAILABLE:
        return f"【{sector_type}板块断层分析】资金断层检测模块不可用"

    try:
        # 转换TPDOG数据格式为dynamic_gap_detector期望的格式
        if sector_df is None or sector_df.empty:
            return f"【{sector_type}板块断层分析】无数据"

        # 创建标准格式的DataFrame
        gap_df = pd.DataFrame()
        gap_df['名称'] = sector_df['name']
        gap_df['今日主力净流入-净额'] = sector_df['m_net'].apply(convert_to_float)

        # 过滤正流入数据
        positive_df = gap_df[gap_df['今日主力净流入-净额'] > 0]
        if len(positive_df) < MIN_SECTORS_FOR_ANALYSIS:
            return f"【{sector_type}板块断层分析】正流入板块数量不足，无法分析（需要至少{MIN_SECTORS_FOR_ANALYSIS}个）"

        # 调用断层分析函数
        gap_report = analyze_funding_gap_v7(
            positive_df,
            sector_type,
            current_time=datetime.now().time(),
            data_dir=None,
            mover_stocks=None,
            stock_flow_data=None,
            ignition_detector=None
        )

        return gap_report

    except Exception as e:
        return f"【{sector_type}板块断层分析】分析失败: {e}"

def analyze_stock_flow_gap_tpdog(stock_df):
    """
    分析个股资金流断层（TPDOG版本）

    Args:
        stock_df: 个股资金流数据DataFrame

    Returns:
        str: 个股断层分析报告
    """
    if not GAP_DETECTOR_AVAILABLE:
        return "【个股资金流断层分析】资金断层检测模块不可用"

    try:
        # 转换TPDOG数据格式为dynamic_gap_detector期望的格式
        if stock_df is None or stock_df.empty:
            return "【个股资金流断层分析】无数据"

        # 创建标准格式的DataFrame
        gap_df = pd.DataFrame()
        gap_df['名称'] = stock_df['name']
        gap_df['今日主力净流入-净额'] = stock_df['income'].apply(convert_to_float)

        # 过滤正流入数据
        positive_df = gap_df[gap_df['今日主力净流入-净额'] > 0]
        if len(positive_df) < MIN_SECTORS_FOR_ANALYSIS:
            return f"【个股资金流断层分析】正流入个股数量不足，无法分析（需要至少{MIN_SECTORS_FOR_ANALYSIS}个）"

        # 创建点火检测器
        ignition_detector = StockFlowIgnitionDetector()

        # 调用个股断层分析函数
        gap_report = analyze_stock_flow_gap(
            positive_df,
            current_time=datetime.now().time(),
            data_dir=None,
            ignition_detector=ignition_detector
        )

        return gap_report

    except Exception as e:
        return f"【个股资金流断层分析】分析失败: {e}"

def get_sector_file_name(sector_code):
    """
    根据版块代码生成文件名

    Args:
        sector_code: 版块代码，如 '880134'

    Returns:
        str: 文件名前缀，如 'BK880134'
    """
    # 直接使用版块代码，添加BK前缀以便识别
    return f"BK{sector_code}"

def get_tpdog_sector_list(sector_type, token):
    """
    获取TPDOG版块列表
    
    Args:
        sector_type: 版块类型 ('bki': 行业版块, 'bkc': 概念版块, 'bkr': 地域版块)
        token: TPDOG API token
    
    Returns:
        DataFrame: 版块列表数据，失败返回None
    """
    try:
        url = f"https://www.tpdog.com/api/bk/list?type={sector_type}&token={token}"
        
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1000 and data.get('content'):
                df = pd.DataFrame(data['content'])
                print(f"✅ 成功获取{sector_type}版块列表: {len(df)} 个版块")
                logging.info(f"成功获取{sector_type}版块列表: {len(df)} 个版块")
                return df
            else:
                raise ValueError(f"TPDOG版块列表API返回错误: {data}")
        else:
            raise ValueError(f"TPDOG版块列表API请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        logging.error(f"获取{sector_type}版块列表失败: {e}")
        print(f"❌ 获取{sector_type}版块列表失败: {e}")
        return None

def get_tpdog_sector_funds(sector_type, token, field='m_net', sort=2):
    """
    获取TPDOG版块资金流数据

    Args:
        sector_type: 版块类型 ('bki': 行业版块, 'bkc': 概念版块, 'bkr': 地域版块)
        token: TPDOG API token
        field: 排序字段，默认按主力净流入排序
        sort: 排序方式，2为倒序（从大到小）

    Returns:
        DataFrame: 版块资金流数据，失败返回None
    """
    try:
        if not is_trading_time():
            print(f"⚠️ 非交易时间，跳过{sector_type}版块资金流获取")
            return None

        url = f"https://www.tpdog.com/api/hs/current/bk_funds?bk_type={sector_type}&field={field}&sort={sort}&token={token}"

        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1000 and data.get('content'):
                df = pd.DataFrame(data['content'])
                print(f"✅ 成功获取{sector_type}版块资金流数据: {len(df)} 条")
                logging.info(f"成功获取{sector_type}版块资金流数据: {len(df)} 条")
                return df
            else:
                raise ValueError(f"TPDOG版块资金流API返回错误: {data}")
        else:
            raise ValueError(f"TPDOG版块资金流API请求失败，状态码: {response.status_code}")

    except Exception as e:
        logging.error(f"获取{sector_type}版块资金流失败: {e}")
        print(f"❌ 获取{sector_type}版块资金流失败: {e}")
        return None

def get_tpdog_stocks_funds(zs_type, token, field='', sort=1, filter=''):
    """
    获取TPDOG股票资金流数据

    Args:
        zs_type: 交易所类型 ('zssh': 上海, 'zssz': 深圳, 'zsbj': 北京)
        token: TPDOG API token
        field: 排序字段，默认为空
        sort: 排序方式，1为正序，2为倒序
        filter: 过滤条件，默认为空

    Returns:
        DataFrame: 股票资金流数据，失败返回None
    """
    try:
        if not is_trading_time():
            print(f"⚠️ 非交易时间，跳过{zs_type}股票资金流获取")
            return None

        url = f"https://www.tpdog.com/api/hs/current/funds?zs_type={zs_type}&filter={filter}&field={field}&sort={sort}&token={token}"

        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1000 and data.get('content'):
                df = pd.DataFrame(data['content'])
                print(f"✅ 成功获取{zs_type}股票资金流数据: {len(df)} 条")
                logging.info(f"成功获取{zs_type}股票资金流数据: {len(df)} 条")
                return df
            else:
                raise ValueError(f"TPDOG股票资金流API返回错误: {data}")
        else:
            raise ValueError(f"TPDOG股票资金流API请求失败，状态码: {response.status_code}")

    except Exception as e:
        logging.error(f"获取{zs_type}股票资金流失败: {e}")
        print(f"❌ 获取{zs_type}股票资金流失败: {e}")
        return None

def get_tpdog_sector_stocks(sector_code, token):
    """
    获取TPDOG版块内个股列表

    Args:
        sector_code: 版块代码，如 'bki.880158'
        token: TPDOG API token

    Returns:
        DataFrame: 个股列表数据，失败返回None
    """
    try:
        url = f"https://www.tpdog.com/api/hs/stocks/list_board?code={sector_code}&token={token}"

        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1000 and data.get('content'):
                df = pd.DataFrame(data['content'])
                print(f"✅ 成功获取版块{sector_code}个股列表: {len(df)} 只股票")
                logging.info(f"成功获取版块{sector_code}个股列表: {len(df)} 只股票")
                return df
            else:
                raise ValueError(f"TPDOG版块个股API返回错误: {data}")
        else:
            raise ValueError(f"TPDOG版块个股API请求失败，状态码: {response.status_code}")

    except Exception as e:
        logging.error(f"获取版块{sector_code}个股列表失败: {e}")
        print(f"❌ 获取版块{sector_code}个股列表失败: {e}")
        return None

def get_sector_stocks_cache_file(sector_code):
    """
    获取版块成分股缓存文件路径

    Args:
        sector_code: 版块代码，如 'bki.880158'

    Returns:
        str: 缓存文件路径
    """
    date_folder = get_date_folder()
    cache_file = os.path.join(date_folder, f'sector_stocks_cache_{sector_code.replace(".", "_")}.csv')
    return cache_file

def get_cached_sector_stocks(sector_code):
    """
    获取缓存的版块成分股数据

    Args:
        sector_code: 版块代码，如 'bki.880158'

    Returns:
        DataFrame: 成分股数据，如果缓存不存在返回None
    """
    cache_file = get_sector_stocks_cache_file(sector_code)

    if os.path.exists(cache_file):
        try:
            stocks_df = pd.read_csv(cache_file, encoding='utf-8-sig')
            print(f"    📋 读取缓存的版块{sector_code}成分股: {len(stocks_df)} 只股票")
            return stocks_df
        except Exception as e:
            print(f"    ⚠️ 读取版块{sector_code}成分股缓存失败: {e}")
            return None

    return None

def cache_sector_stocks(sector_code, stocks_df):
    """
    缓存版块成分股数据

    Args:
        sector_code: 版块代码，如 'bki.880158'
        stocks_df: 成分股数据
    """
    try:
        cache_file = get_sector_stocks_cache_file(sector_code)
        stocks_df.to_csv(cache_file, index=False, encoding='utf-8-sig')
        print(f"    💾 已缓存版块{sector_code}成分股数据到: {cache_file}")
        logging.info(f"版块{sector_code}成分股数据已缓存: {cache_file}")
    except Exception as e:
        print(f"    ⚠️ 缓存版块{sector_code}成分股数据失败: {e}")
        logging.error(f"缓存版块{sector_code}成分股数据失败: {e}")

# 全市场股票资金流数据缓存
_market_funds_cache = None
_market_funds_cache_time = None

def get_market_funds_cache_file():
    """
    获取全市场股票资金流缓存文件路径

    Returns:
        str: 缓存文件路径
    """
    date_folder = get_date_folder()
    timestamp = get_timestamp()
    cache_file = os.path.join(date_folder, f'market_funds_cache_{timestamp[:8]}.csv')
    return cache_file

def get_cached_market_funds():
    """
    获取缓存的全市场股票资金流数据

    Returns:
        DataFrame: 全市场股票资金流数据，如果缓存不存在或过期返回None
    """
    global _market_funds_cache, _market_funds_cache_time

    # 检查内存缓存是否有效（1分钟内）
    current_time = __import__('time').time()
    if (_market_funds_cache is not None and
        _market_funds_cache_time is not None and
        current_time - _market_funds_cache_time < 60):
        print(f"    📋 使用内存缓存的全市场股票资金流数据: {len(_market_funds_cache)} 只股票")
        return _market_funds_cache.copy()

    return None

def cache_market_funds(funds_df):
    """
    缓存全市场股票资金流数据

    Args:
        funds_df: 全市场股票资金流数据
    """
    global _market_funds_cache, _market_funds_cache_time

    try:
        # 更新内存缓存
        _market_funds_cache = funds_df.copy()
        _market_funds_cache_time = __import__('time').time()

        # 保存到文件（可选）
        cache_file = get_market_funds_cache_file()
        funds_df.to_csv(cache_file, index=False, encoding='utf-8-sig')
        print(f"    💾 已缓存全市场股票资金流数据: {len(funds_df)} 只股票")
        logging.info(f"全市场股票资金流数据已缓存: {cache_file}")
    except Exception as e:
        print(f"    ⚠️ 缓存全市场股票资金流数据失败: {e}")
        logging.error(f"缓存全市场股票资金流数据失败: {e}")

def clear_market_funds_cache():
    """
    清理全市场股票资金流数据缓存
    """
    global _market_funds_cache, _market_funds_cache_time

    _market_funds_cache = None
    _market_funds_cache_time = None
    print(f"    🧹 已清理全市场股票资金流数据缓存")
    logging.info("全市场股票资金流数据缓存已清理")

def get_cache_status():
    """
    获取缓存状态信息

    Returns:
        dict: 缓存状态信息
    """
    global _market_funds_cache, _market_funds_cache_time

    current_time = __import__('time').time()
    status = {
        'has_cache': _market_funds_cache is not None,
        'cache_time': _market_funds_cache_time,
        'cache_age': current_time - _market_funds_cache_time if _market_funds_cache_time else None,
        'cache_size': len(_market_funds_cache) if _market_funds_cache is not None else 0,
        'is_valid': (_market_funds_cache is not None and
                    _market_funds_cache_time is not None and
                    current_time - _market_funds_cache_time < 60)
    }
    return status

def get_all_market_funds(token, force_refresh=False):
    """
    获取全市场股票资金流数据（带缓存）

    Args:
        token: TPDOG API token
        force_refresh: 是否强制刷新缓存，默认False

    Returns:
        DataFrame: 全市场股票资金流数据，失败返回None
    """
    # 如果强制刷新，先清理缓存
    if force_refresh:
        clear_market_funds_cache()

    # 首先尝试从缓存获取
    cached_funds = get_cached_market_funds()
    if cached_funds is not None:
        return cached_funds

    # 缓存不存在或过期，从API获取
    print(f"    🔄 获取全市场股票资金流数据...")
    all_stocks_funds = []

    # 分别获取上海和深圳的股票资金流数据
    for exchange in ['zssh', 'zssz']:
        try:
            if not is_trading_time():
                # 非交易时间使用测试参数
                url = f"https://www.tpdog.com/api/hs/current/scans?zs_type={exchange}&sort=2&field=income&filter=&t=1&token={token}"
            else:
                url = f"https://www.tpdog.com/api/hs/current/scans?zs_type={exchange}&sort=2&field=income&filter=&token={token}"

            response = requests.get(url, timeout=15)
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 1000 and data.get('content'):
                    exchange_stocks = pd.DataFrame(data['content'])
                    all_stocks_funds.append(exchange_stocks)
                    print(f"    ✅ 获取{exchange}股票资金流数据: {len(exchange_stocks)} 只股票")
                elif data.get('code') == 1002:
                    # 示例数据
                    exchange_stocks = pd.DataFrame(data.get('content', []))
                    if not exchange_stocks.empty:
                        all_stocks_funds.append(exchange_stocks)
                        print(f"    ⚠️ 获取{exchange}示例资金流数据: {len(exchange_stocks)} 只股票")

            # 避免请求过于频繁
            time_module.sleep(0.3)

        except Exception as e:
            print(f"    ❌ 获取{exchange}股票资金流失败: {e}")
            continue

    if not all_stocks_funds:
        print(f"    ❌ 未能获取任何股票资金流数据")
        return None

    # 合并所有交易所的数据
    all_funds_df = pd.concat(all_stocks_funds, ignore_index=True)

    # 缓存数据
    cache_market_funds(all_funds_df)

    return all_funds_df

def get_tpdog_sector_stocks_funds(sector_code, token, force_refresh_market=False):
    """
    获取TPDOG版块内个股的资金流数据（使用成分股缓存和全市场资金流缓存）

    Args:
        sector_code: 版块代码，如 'bki.880158'
        token: TPDOG API token
        force_refresh_market: 是否强制刷新全市场数据缓存，默认False

    Returns:
        DataFrame: 个股资金流数据，失败返回None
    """
    try:
        # 首先尝试从缓存获取版块内的个股列表
        stocks_df = get_cached_sector_stocks(sector_code)

        if stocks_df is None:
            # 缓存不存在，从API获取并缓存
            print(f"    🔄 首次获取版块{sector_code}成分股...")
            stocks_df = get_tpdog_sector_stocks(sector_code, token)
            if stocks_df is None or stocks_df.empty:
                return None

            # 缓存成分股数据
            cache_sector_stocks(sector_code, stocks_df)

        # 获取全市场股票资金流数据（使用缓存）
        all_funds_df = get_all_market_funds(token, force_refresh=force_refresh_market)
        if all_funds_df is None or all_funds_df.empty:
            print(f"    ❌ 未能获取全市场股票资金流数据")
            return None

        # 筛选出版块内的个股
        sector_stocks_codes = set(stocks_df['code'].astype(str))
        sector_funds_df = all_funds_df[all_funds_df['code'].astype(str).isin(sector_stocks_codes)]

        if sector_funds_df.empty:
            print(f"    ⚠️ 版块{sector_code}内未找到匹配的个股资金流数据")
            return None

        print(f"    ✅ 成功获取版块{sector_code}内{len(sector_funds_df)}只个股的资金流数据")
        logging.info(f"成功获取版块{sector_code}内{len(sector_funds_df)}只个股的资金流数据")
        return sector_funds_df

    except Exception as e:
        logging.error(f"获取版块{sector_code}个股资金流失败: {e}")
        print(f"❌ 获取版块{sector_code}个股资金流失败: {e}")
        return None

def get_tpdog_sector_quote(sector_type, sector_code, token):
    """
    获取TPDOG版块实时行情数据（使用版块筛选接口）

    Args:
        sector_type: 版块类型 ('bki': 行业版块, 'bkc': 概念版块, 'bkr': 地域版块)
        sector_code: 版块代码，如 '880158'
        token: TPDOG API token

    Returns:
        dict: 版块行情数据，失败返回None
    """
    try:
        # 使用版块筛选接口获取特定版块的行情数据
        if not is_trading_time():
            # 非交易时间使用测试参数
            url = f"https://www.tpdog.com/api/current/bk_scans?bk_type={sector_type}&sort=1&field=code&filter=code={sector_code}&t=1&token={token}"
        else:
            url = f"https://www.tpdog.com/api/current/bk_scans?bk_type={sector_type}&sort=1&field=code&filter=code={sector_code}&token={token}"

        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1000 and data.get('content'):
                # 返回匹配的版块数据（应该只有一个）
                content = data['content']
                if content and len(content) > 0:
                    return content[0]  # 返回第一个匹配的版块
                else:
                    print(f"⚠️ 版块{sector_type}.{sector_code}未找到行情数据")
                    return None
            elif data.get('code') == 1002:
                # 示例数据，非交易时间返回的测试数据
                print(f"⚠️ 版块{sector_type}.{sector_code}返回示例数据（非交易时间）")
                content = data.get('content', [])
                return content[0] if content else None
            else:
                raise ValueError(f"TPDOG版块行情API返回错误: {data}")
        else:
            raise ValueError(f"TPDOG版块行情API请求失败，状态码: {response.status_code}")

    except Exception as e:
        logging.error(f"获取版块{sector_type}.{sector_code}行情失败: {e}")
        print(f"❌ 获取版块{sector_type}.{sector_code}行情失败: {e}")
        return None

def check_and_update_sector_lists():
    """
    检查并更新版块列表（收盘时执行）
    如果版块列表文件不存在，则获取并保存
    """
    try:
        date_folder = get_date_folder()
        timestamp = get_timestamp()

        # 检查行业版块列表
        industry_list_file = os.path.join(date_folder, 'tpdog_industry_list.json')
        if not os.path.exists(industry_list_file):
            print("🔄 获取行业版块列表...")
            industry_df = get_tpdog_sector_list('bki', TPDOG_TOKEN)
            if industry_df is not None:
                # 保存JSON缓存文件（用于判断是否需要重新获取）
                industry_df.to_json(industry_list_file, orient='records', indent=2)
                print(f"✅ 行业版块列表已保存: {len(industry_df)} 个版块")
                logging.info(f"行业版块列表已保存到: {industry_list_file}")
        else:
            print("✅ 行业版块列表已存在，跳过获取")

        # 检查概念版块列表
        concept_list_file = os.path.join(date_folder, 'tpdog_concept_list.json')
        if not os.path.exists(concept_list_file):
            print("🔄 获取概念版块列表...")
            concept_df = get_tpdog_sector_list('bkc', TPDOG_TOKEN)
            if concept_df is not None:
                # 保存JSON缓存文件（用于判断是否需要重新获取）
                concept_df.to_json(concept_list_file, orient='records', indent=2)
                print(f"✅ 概念版块列表已保存: {len(concept_df)} 个版块")
                logging.info(f"概念版块列表已保存到: {concept_list_file}")
        else:
            print("✅ 概念版块列表已存在，跳过获取")

    except Exception as e:
        logging.error(f"检查更新版块列表失败: {e}")
        print(f"❌ 检查更新版块列表失败: {e}")

def get_sector_funds_data():
    """
    获取版块资金流数据（交易时间执行）
    """
    try:
        if not is_trading_time():
            print("⚠️ 非交易时间，跳过版块资金流获取")
            return

        date_folder = get_date_folder()
        timestamp = get_timestamp()

        print("\n" + "=" * 50)
        print("📊 开始获取TPDOG版块资金流数据")
        print("=" * 50)

        # 1. 获取行业版块资金流
        print("🔄 获取行业版块资金流...")
        industry_funds_df = get_tpdog_sector_funds('bki', TPDOG_TOKEN)
        if industry_funds_df is not None:
            # 保存完整数据
            industry_funds_file = os.path.join(date_folder, f'tpdog_industry_funds_{timestamp}.csv')
            industry_funds_df.to_csv(industry_funds_file, index=False, encoding='utf-8-sig')

            # 打印前10名
            print("\n📈 行业版块资金流前10名:")
            top_10_industry = industry_funds_df.head(10)
            for idx, row in top_10_industry.iterrows():
                print(f"  {idx+1:2d}. {row['name']:15s} | 主力净流入: {format_amount_local(row['m_net']):>10s} | 净流入比例: {row.get('m_in_ratio', 0):6.2f}%")

            logging.info(f"行业版块资金流数据已保存: {industry_funds_file}")

            # 进行行业板块资金断层分析
            print("\n" + "="*60)
            industry_gap_report = analyze_sector_gap(industry_funds_df, "行业")
            print(industry_gap_report)
            print("="*60)

        # 2. 获取概念版块资金流
        print("\n🔄 获取概念版块资金流...")
        concept_funds_df = get_tpdog_sector_funds('bkc', TPDOG_TOKEN)
        if concept_funds_df is not None:
            # 保存完整数据
            concept_funds_file = os.path.join(date_folder, f'tpdog_concept_funds_{timestamp}.csv')
            concept_funds_df.to_csv(concept_funds_file, index=False, encoding='utf-8-sig')

            # 打印前10名
            print("\n📈 概念版块资金流前10名:")
            top_10_concept = concept_funds_df.head(10)
            for idx, row in top_10_concept.iterrows():
                print(f"  {idx+1:2d}. {row['name']:15s} | 主力净流入: {format_amount_local(row['m_net']):>10s} | 净流入比例: {row.get('m_in_ratio', 0):6.2f}%")

            logging.info(f"概念版块资金流数据已保存: {concept_funds_file}")

            # 进行概念板块资金断层分析
            print("\n" + "="*60)
            concept_gap_report = analyze_sector_gap(concept_funds_df, "概念")
            print(concept_gap_report)
            print("="*60)

        # 3. 获取股票资金流数据（使用funds接口）
        print("\n🔄 获取股票资金流数据...")
        all_stocks_funds_data = []

        # 分别获取上海、深圳、北京的股票资金流数据
        for exchange in ['zssh', 'zssz', 'zsbj']:
            exchange_name = {'zssh': '上海', 'zssz': '深圳', 'zsbj': '北京'}[exchange]
            print(f"  🔄 获取{exchange_name}股票资金流数据...")

            stocks_funds_df = get_tpdog_stocks_funds(exchange, TPDOG_TOKEN, field='m_net', sort=2)
            if stocks_funds_df is not None and not stocks_funds_df.empty:
                # 添加交易所标识
                stocks_funds_df['exchange'] = exchange_name
                all_stocks_funds_data.append(stocks_funds_df)

                # 保存单个交易所的数据
                exchange_funds_file = os.path.join(date_folder, f'tpdog_stocks_funds_{exchange}_{timestamp}.csv')
                stocks_funds_df.to_csv(exchange_funds_file, index=False, encoding='utf-8-sig')

                # 打印前5名
                print(f"    📈 {exchange_name}股票资金流前5名:")
                top_5_stocks = stocks_funds_df.head(5)
                for idx, row in top_5_stocks.iterrows():
                    print(f"      {idx+1}. {row['name']:10s} | 主力净流入: {format_amount_local(row['m_net']):>10s} | 主力流入比例: {row.get('m_in_ratio', 0):6.2f}%")

                logging.info(f"{exchange_name}股票资金流数据已保存: {exchange_funds_file}")

            # 避免请求过于频繁
            time_module.sleep(0.5)

        # 合并所有交易所的股票资金流数据
        if all_stocks_funds_data:
            combined_stocks_funds_df = pd.concat(all_stocks_funds_data, ignore_index=True)
            # 按主力净流入排序
            combined_stocks_funds_df = combined_stocks_funds_df.sort_values('m_net', ascending=False)

            # 保存合并后的数据
            combined_funds_file = os.path.join(date_folder, f'tpdog_stocks_funds_combined_{timestamp}.csv')
            combined_stocks_funds_df.to_csv(combined_funds_file, index=False, encoding='utf-8-sig')

            # 打印全市场前10名
            print(f"\n📈 全市场股票资金流前10名:")
            top_10_all = combined_stocks_funds_df.head(10)
            for idx, row in top_10_all.iterrows():
                print(f"  {idx+1:2d}. {row['name']:15s} ({row['exchange']}) | 主力净流入: {format_amount_local(row['m_net']):>10s} | 主力流入比例: {row.get('m_in_ratio', 0):6.2f}%")

            print(f"✅ 合并股票资金流数据已保存: {combined_funds_file} (共{len(combined_stocks_funds_df)}只股票)")
            logging.info(f"合并股票资金流数据已保存: {combined_funds_file}")
        else:
            print("❌ 未能获取任何股票资金流数据")

        # 5. 获取全市场个股资金流数据并进行断层分析（使用scans接口）
        print("\n🔄 获取全市场个股资金流数据（scans接口）...")
        market_funds_df = get_all_market_funds(TPDOG_TOKEN, force_refresh=True)
        if market_funds_df is not None and not market_funds_df.empty:
            # 按主力净流入排序
            market_funds_df = market_funds_df.sort_values('income', ascending=False)

            # 保存全市场个股资金流数据
            market_funds_file = os.path.join(date_folder, f'tpdog_market_stocks_funds_{timestamp}.csv')
            market_funds_df.to_csv(market_funds_file, index=False, encoding='utf-8-sig')

            # 打印前10名个股资金流
            print("\n📈 全市场个股资金流前10名:")
            top_10_stocks = market_funds_df.head(10)
            for idx, row in top_10_stocks.iterrows():
                print(f"  {idx+1:2d}. {row['name']:15s} | 主力净流入: {format_amount_local(row['income']):>10s}")

            logging.info(f"全市场个股资金流数据已保存: {market_funds_file}")

            # 进行个股资金流断层分析
            print("\n" + "="*60)
            stock_gap_report = analyze_stock_flow_gap_tpdog(market_funds_df)
            print(stock_gap_report)
            print("="*60)
        else:
            print("❌ 未能获取全市场个股资金流数据")

        # 6. 获取前10版块的个股数据
        get_top_sectors_stocks_data(industry_funds_df, concept_funds_df)

        # 7. 获取前10版块的行情数据
        get_top_sectors_quotes_data(industry_funds_df, concept_funds_df)

        print("=" * 50)
        print("✅ TPDOG版块数据获取完成")
        print("=" * 50)

    except Exception as e:
        logging.error(f"获取版块资金流数据失败: {e}")
        print(f"❌ 获取版块资金流数据失败: {e}")

def get_top_sectors_stocks_data(industry_df, concept_df):
    """
    获取前10版块的个股资金流数据
    """
    try:
        import re
        date_folder = get_date_folder()
        timestamp = get_timestamp()

        print("\n🔄 获取前10版块的个股资金流数据...")

        # 在开始批量处理前清理全市场资金流缓存，确保获取最新数据
        print("    🧹 清理全市场资金流缓存，准备获取最新数据...")
        clear_market_funds_cache()

        # 处理行业版块前10
        if industry_df is not None and not industry_df.empty:
            top_10_industry = industry_df.head(10)
            for idx, row in top_10_industry.iterrows():
                sector_code = f"bki.{row['code']}"
                sector_name = row['name']
                sector_code_only = row['code']  # 纯数字代码

                print(f"  💰 获取行业版块 '{sector_name}' 的个股资金流...")
                # 第一个版块强制刷新全市场数据，后续版块使用缓存
                force_refresh = (idx == 0)
                stocks_funds_df = get_tpdog_sector_stocks_funds(sector_code, TPDOG_TOKEN, force_refresh_market=force_refresh)

                if stocks_funds_df is not None and not stocks_funds_df.empty:
                    # 按主力净流入排序
                    stocks_funds_df = stocks_funds_df.sort_values('income', ascending=False)

                    # 使用版块代码生成文件名
                    file_name_prefix = get_sector_file_name(sector_code_only)
                    stocks_file = os.path.join(date_folder, f'{file_name_prefix}_Industry_Stocks_Funds_{timestamp}.csv')
                    stocks_funds_df.to_csv(stocks_file, index=False, encoding='utf-8-sig')

                    # 打印前5名个股资金流
                    print(f"    📈 {sector_name} 个股资金流前5名:")
                    top_5_stocks = stocks_funds_df.head(5)
                    for i, stock_row in top_5_stocks.iterrows():
                        print(f"      {i+1}. {stock_row['name']:10s} | 主力流入: {format_amount_local(stock_row['income']):>10s}")

                    print(f"    ✅ 已保存 {len(stocks_funds_df)} 只个股资金流数据到: {stocks_file}")
                    logging.info(f"行业版块'{sector_name}'个股资金流数据已保存: {stocks_file}")

                # 避免请求过于频繁
                time_module.sleep(0.5)

        # 处理概念版块前10
        if concept_df is not None and not concept_df.empty:
            top_10_concept = concept_df.head(10)
            for idx, row in top_10_concept.iterrows():
                sector_code = f"bkc.{row['code']}"
                sector_name = row['name']
                sector_code_only = row['code']  # 纯数字代码

                print(f"  💰 获取概念版块 '{sector_name}' 的个股资金流...")
                # 概念版块都使用缓存，不强制刷新（因为行业版块已经获取了最新数据）
                stocks_funds_df = get_tpdog_sector_stocks_funds(sector_code, TPDOG_TOKEN, force_refresh_market=False)

                if stocks_funds_df is not None and not stocks_funds_df.empty:
                    # 按主力净流入排序
                    stocks_funds_df = stocks_funds_df.sort_values('income', ascending=False)

                    # 使用版块代码生成文件名
                    file_name_prefix = get_sector_file_name(sector_code_only)
                    stocks_file = os.path.join(date_folder, f'{file_name_prefix}_Concept_Stocks_Funds_{timestamp}.csv')
                    stocks_funds_df.to_csv(stocks_file, index=False, encoding='utf-8-sig')

                    # 打印前5名个股资金流
                    print(f"    📈 {sector_name} 个股资金流前5名:")
                    top_5_stocks = stocks_funds_df.head(5)
                    for i, stock_row in top_5_stocks.iterrows():
                        print(f"      {i+1}. {stock_row['name']:10s} | 主力流入: {format_amount_local(stock_row['income']):>10s}")

                    print(f"    ✅ 已保存 {len(stocks_funds_df)} 只个股资金流数据到: {stocks_file}")
                    logging.info(f"概念版块'{sector_name}'个股资金流数据已保存: {stocks_file}")

                # 避免请求过于频繁
                time_module.sleep(0.5)

    except Exception as e:
        logging.error(f"获取前10版块个股资金流数据失败: {e}")
        print(f"❌ 获取前10版块个股资金流数据失败: {e}")

def get_top_sectors_quotes_data(industry_df, concept_df):
    """
    获取前10版块的行情数据
    """
    try:
        date_folder = get_date_folder()
        timestamp = get_timestamp()

        print("\n🔄 获取前10版块的行情数据...")

        all_quotes = []

        # 处理行业版块前10
        if industry_df is not None and not industry_df.empty:
            top_10_industry = industry_df.head(10)
            for idx, row in top_10_industry.iterrows():
                sector_type = 'bki'
                sector_code = row['code']
                sector_name = row['name']

                quote_data = get_tpdog_sector_quote(sector_type, sector_code, TPDOG_TOKEN)
                if quote_data:
                    quote_data['sector_type'] = '行业'
                    quote_data['sector_name'] = sector_name
                    quote_data['sector_code'] = f"{sector_type}.{sector_code}"
                    all_quotes.append(quote_data)

                # 避免请求过于频繁
                time_module.sleep(0.3)

        # 处理概念版块前10
        if concept_df is not None and not concept_df.empty:
            top_10_concept = concept_df.head(10)
            for idx, row in top_10_concept.iterrows():
                sector_type = 'bkc'
                sector_code = row['code']
                sector_name = row['name']

                quote_data = get_tpdog_sector_quote(sector_type, sector_code, TPDOG_TOKEN)
                if quote_data:
                    quote_data['sector_type'] = '概念'
                    quote_data['sector_name'] = sector_name
                    quote_data['sector_code'] = f"{sector_type}.{sector_code}"
                    all_quotes.append(quote_data)

                # 避免请求过于频繁
                time_module.sleep(0.3)

        # 保存所有行情数据
        if all_quotes:
            quotes_df = pd.DataFrame(all_quotes)
            quotes_file = os.path.join(date_folder, f'tpdog_sectors_quotes_{timestamp}.csv')
            quotes_df.to_csv(quotes_file, index=False, encoding='utf-8-sig')

            # 打印涨幅前10的版块
            if 'rise_rate' in quotes_df.columns:
                quotes_df['rise_rate'] = pd.to_numeric(quotes_df['rise_rate'], errors='coerce')
                top_gainers = quotes_df.nlargest(10, 'rise_rate')

                print("\n📈 涨幅前10的版块:")
                for idx, row in top_gainers.iterrows():
                    print(f"  {idx+1:2d}. {row['sector_name']:15s} ({row['sector_type']}) | 涨跌幅: {row['rise_rate']:6.2f}% | 最新价: {row.get('price', 0):8.2f}")

            print(f"✅ 版块行情数据已保存: {quotes_file} ({len(all_quotes)} 个版块)")
            logging.info(f"版块行情数据已保存: {quotes_file}")

    except Exception as e:
        logging.error(f"获取前10版块行情数据失败: {e}")
        print(f"❌ 获取前10版块行情数据失败: {e}")

def run_threaded(job_func):
    """在后台线程中运行任务"""
    job_thread = threading.Thread(target=job_func)
    job_thread.daemon = True
    job_thread.start()

def schedule_jobs():
    """设置定时任务"""
    # 任务1: 收盘后检查版块列表（每日15:10执行）
    schedule.every().day.at("15:10").do(run_threaded, check_and_update_sector_lists)

    # 任务2: 交易时间每2分钟获取版块资金流数据
    schedule.every(2).minutes.do(run_threaded, get_sector_funds_data)

    print("✅ 定时任务已设置:")
    print("   - 每日15:10检查更新版块列表")
    print("   - 每2分钟获取版块资金流数据（仅交易时间）")
    logging.info("定时任务已设置: 每日15:10检查版块列表，每2分钟获取资金流数据")

def main():
    """主程序"""
    print("=" * 60)
    print("🚀 TPDOG版块资金流监控程序启动")
    print("=" * 60)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"交易时间: 09:30-11:30 / 13:00-15:00")
    print(f"数据获取频率: 每2分钟")
    print("=" * 60)

    # 设置日志
    setup_logging()

    # 首次启动时立即执行检查
    print("\n🔄 首次启动，立即执行版块列表检查...")
    check_and_update_sector_lists()

    # 如果是交易时间，立即执行一次数据获取
    if is_trading_time():
        print("\n🔄 当前为交易时间，立即执行一次数据获取...")
        get_sector_funds_data()
    else:
        print("\n⏰ 当前非交易时间，等待交易时间开始...")

    # 设置定时任务
    schedule_jobs()

    # 主循环
    print("\n🔄 进入监控循环，按Ctrl+C退出...")
    try:
        while True:
            schedule.run_pending()
            time_module.sleep(30)  # 每30秒检查一次定时任务
    except KeyboardInterrupt:
        print("\n\n👋 程序已停止")
        logging.info("程序手动停止")

if __name__ == "__main__":
    main()
